# BIDS Audio Dataset Converter Toolkit

This repository contains a collection of Python scripts for preparing, validating, and fixing audio datasets according to BIDS (Brain Imaging Data Structure) conventions.

## Directory Structure

- **delete-invalid-files/**: Scripts for removing problematic files
- **find-invalid-files/**: Scripts for identifying files that don't conform to BIDS conventions
- **fix-invalid-files/**: <PERSON>ripts for correcting various naming and formatting issues
- **move-files/**: <PERSON>ripts for reorganizing files within the dataset structure

## Installation

Install the required dependencies using the provided requirements.txt file:

```bash
pip install -r requirements.txt
```

This will install:
- pandas: For CSV file processing
- numpy: For numerical operations
- pathlib: For improved file path handling

## Script Documentation

### Delete Invalid Files

#### `delete_na_task_files.py`

Removes files with invalid 'n/a' task codes in their filenames.

```bash
python delete-invalid-files/delete_na_task_files.py /path/to/dataset
```

### Find Invalid Files

#### `find_files_with_multiple_periods.py`

Identifies files with multiple periods in their filenames, which may cause issues with BIDS validation.

```bash
python find-invalid-files/find_files_with_multiple_periods.py /path/to/dataset
```

#### `find_invalid_task_codes.py`

Scans for files with task codes that don't match the allowed list (CELFS, CELFT, GFTA, PCT, PLSS, TPL).

```bash
python find-invalid-files/find_invalid_task_codes.py /path/to/dataset
```

### Fix Invalid Files

#### `fix_naming_conventions.py`

Comprehensive tool for fixing various naming convention issues:
- Microphone acquisition codes (micX → MicX, micY → MicY, micXY → MicXY)
- QC labels (postqc → PostQC, preqc → PreQC)
- General BIDS naming conventions (desc tags, multiple underscores, etc.)

```bash
# Apply all fixes
python fix-invalid-files/fix_naming_conventions.py /path/to/dataset --all

# Apply only specific fixes
python fix-invalid-files/fix_naming_conventions.py /path/to/dataset --fix-mic --fix-qc

# Preview changes without modifying files
python fix-invalid-files/fix_naming_conventions.py /path/to/dataset --all --dry-run
```

#### `fix_task_codes.py`

Fixes inconsistent task codes in filenames.

```bash
python fix-invalid-files/fix_task_codes.py /path/to/dataset
```

#### `fix_task_run_numbering.py`

Corrects and standardizes run numbering in filenames.

```bash
python fix-invalid-files/fix_task_run_numbering.py /path/to/dataset
```

#### `fix_desc_format.py`

Fixes the format of description tags in filenames.

```bash
python fix-invalid-files/fix_desc_format.py /path/to/dataset
```

### Move Files

#### `manage_warmup_data.py`

Comprehensive tool for handling warmup data:
- Moves files with _task-warmup or _task-WRM in their names to a separate directory
- Removes rows with _task-warmup from CSV files

```bash
# Move warmup files only
python move-files/manage_warmup_data.py /path/to/source --dst_root /path/to/destination --move-files

# Remove warmup rows from CSV files only
python move-files/manage_warmup_data.py /path/to/source --remove-rows

# Do both operations
python move-files/manage_warmup_data.py /path/to/source --dst_root /path/to/destination --move-files --remove-rows
```

#### `move_ftl_files.py`

Moves FTL (Failed to Label) audio files to a separate directory.

```bash
python move-files/move_ftl_files.py /path/to/source /path/to/destination
```

## Recommended Workflow

For a complete dataset cleanup, we recommend following this sequence:

1. **Find Issues**: Run the find-invalid-files scripts to identify problems
   ```bash
   python find-invalid-files/find_invalid_task_codes.py /path/to/dataset
   python find-invalid-files/find_files_with_multiple_periods.py /path/to/dataset
   ```

2. **Move Special Files**: Relocate special file types
   ```bash
   python move-files/manage_warmup_data.py /path/to/dataset --dst_root /path/to/warmup_files --move-files
   python move-files/move_ftl_files.py /path/to/dataset /path/to/ftl_files
   ```

3. **Fix Naming Issues**: Apply naming convention fixes
   ```bash
   python fix-invalid-files/fix_naming_conventions.py /path/to/dataset --all
   python fix-invalid-files/fix_task_codes.py /path/to/dataset
   python fix-invalid-files/fix_task_run_numbering.py /path/to/dataset
   ```

4. **Clean Up Remaining Issues**: Process CSV files and remove invalid files
   ```bash
   python move-files/manage_warmup_data.py /path/to/dataset --remove-rows
   python delete-invalid-files/delete_na_task_files.py /path/to/dataset
   ```

## Audit Logs

Most scripts create detailed logs of their operations, which can be found in:
- JSON format audit logs in the dataset's `audit_logs/` directory
- CSV files listing problematic files
- Text files with details of changed files

## Notes

- Always run scripts with the `--dry-run` flag first (when available) to preview changes
- Back up your data before running scripts that modify files
- Check the audit logs after each operation to verify the changes made
