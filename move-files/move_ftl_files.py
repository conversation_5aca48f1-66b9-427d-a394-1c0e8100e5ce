import os
import shutil
import argparse
import datetime
import json
import re

parser = argparse.ArgumentParser(description="Move all files with FTL in task identifier (_task-*_acq) to a target directory, maintaining folder structure.")
parser.add_argument('source_root', help='Source root directory to search for files')
parser.add_argument('target_root', help='Target root directory (e.g., large_files)')
args = parser.parse_args()

audit_log = []
# Pattern to match FTL anywhere between _task- and _acq
ftl_pattern = re.compile(r'_task-[^_]*FTL[^_]*_acq')

for dirpath, dirnames, filenames in os.walk(args.source_root):
    for fname in filenames:
        if '_task-FTL' in fname or ftl_pattern.search(fname):
            src_path = os.path.join(dirpath, fname)
            rel_path = os.path.relpath(src_path, args.source_root)
            dest_path = os.path.join(args.target_root, rel_path)
            dest_dir = os.path.dirname(dest_path)
            os.makedirs(dest_dir, exist_ok=True)
            shutil.move(src_path, dest_path)
            audit_log.append({
                'src_path': src_path,
                'dest_path': dest_path,
                'timestamp': datetime.datetime.now().isoformat()
            })

# Write audit log
if audit_log:
    log_name = f"move_ftl_audit_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    log_path = os.path.join(args.target_root, log_name)
    with open(log_path, 'w') as f:
        json.dump(audit_log, f, indent=2)
    print(f"Moved {len(audit_log)} files. Audit log written to {log_path}")
else:
    print("No FTL task files found.") 