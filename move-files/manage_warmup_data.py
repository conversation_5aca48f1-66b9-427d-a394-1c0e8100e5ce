import os
import shutil
import pandas as pd
import json
import argparse
from datetime import datetime

def add_timestamp_to_filename(filename):
    """Add a timestamp to a filename to make it unique."""
    base, ext = os.path.splitext(filename)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{base}_{timestamp}{ext}"

def move_warmup_files(src_root, dst_root, audit_log=None):
    """
    Move all files with _task-warmup or _task-WRM in the name to a target folder, 
    maintaining folder structure.
    
    Args:
        src_root: Source root directory to search
        dst_root: Target root directory to move files to
        audit_log: List to append audit entries to (or None to create a new list)
    
    Returns:
        List of audit log entries
    """
    if audit_log is None:
        audit_log = []
    
    for dirpath, dirnames, filenames in os.walk(src_root):
        for filename in filenames:
            if '_task-warmup' in filename or '_task-WRM' in filename:
                src_file = os.path.join(dirpath, filename)
                # Compute relative path from src_root
                rel_path = os.path.relpath(dirpath, src_root)
                dst_dir = os.path.join(dst_root, rel_path)
                os.makedirs(dst_dir, exist_ok=True)
                dst_file = os.path.join(dst_dir, filename)
                shutil.move(src_file, dst_file)
                audit_log.append({
                    'action': 'move',
                    'source': src_file,
                    'destination': dst_file
                })
                print(f"Moved: {src_file} -> {dst_file}")
    
    return audit_log

def remove_warmup_rows(root_dir, audit_log=None):
    """
    Remove rows with _task-warmup in the first column from any CSV file 
    containing preqc.csv or postqc.csv in its name.
    
    Args:
        root_dir: Root directory to search
        audit_log: List to append audit entries to (or None to create a new list)
    
    Returns:
        List of audit log entries
    """
    if audit_log is None:
        audit_log = []
    
    found = False
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            if 'preqc.csv' in filename.lower() or 'postqc.csv' in filename.lower():
                found = True
                file_path = os.path.join(dirpath, filename)
                print(f"Checking: {file_path}")
                try:
                    df = pd.read_csv(file_path)
                    original_count = len(df)
                    first_col = df.columns[0]
                    # Find rows to remove
                    to_remove = df[df[first_col].astype(str).str.contains('_task-warmup')]
                    removed_values = to_remove[first_col].tolist()
                    # Remove rows
                    mask = ~df[first_col].astype(str).str.contains('_task-warmup')
                    filtered_df = df[mask]
                    removed_count = original_count - len(filtered_df)
                    filtered_df.to_csv(file_path, index=False)
                    print(f"Processed {file_path}: removed {removed_count} rows.")
                    if removed_count > 0:
                        audit_log.append({
                            'action': 'remove_rows',
                            'file': file_path,
                            'removed_rows': removed_values
                        })
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
    
    if not found:
        print("No matching preqc.csv or postqc.csv files found.")
    
    return audit_log

def save_audit_log(audit_log, audit_log_path=None):
    """Save the audit log to a JSON file with a timestamp."""
    if audit_log_path is None:
        audit_log_path = add_timestamp_to_filename('warmup_management_audit.json')
    else:
        audit_log_path = add_timestamp_to_filename(audit_log_path)
    
    with open(audit_log_path, 'w') as f:
        json.dump(audit_log, f, indent=2)
    
    print(f"Audit log written to {audit_log_path}")
    return audit_log_path

def manage_warmup_data(src_root, dst_root=None, remove_rows=False, move_files=False, audit_log_path=None):
    """
    Manage warmup data by moving files and/or removing rows from CSV files.
    
    Args:
        src_root: Source root directory to search
        dst_root: Target root directory to move files to (required if move_files is True)
        remove_rows: Whether to remove warmup rows from CSV files
        move_files: Whether to move warmup files
        audit_log_path: Path to write the audit log JSON (timestamp will be added)
    """
    # Validate arguments
    if move_files and not dst_root:
        raise ValueError("dst_root is required when move_files is True")
    
    if not remove_rows and not move_files:
        print("No actions specified. Please specify at least one action (remove_rows or move_files).")
        return
    
    # Initialize audit log
    audit_log = []
    
    # Perform requested actions
    if move_files:
        print(f"Moving warmup files from {src_root} to {dst_root}...")
        audit_log = move_warmup_files(src_root, dst_root, audit_log)
    
    if remove_rows:
        print(f"Removing warmup rows from CSV files in {src_root}...")
        audit_log = remove_warmup_rows(src_root, audit_log)
    
    # Save audit log
    if audit_log:
        save_audit_log(audit_log, audit_log_path)
    else:
        print("No changes were made.")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Manage warmup data by moving files and/or removing rows from CSV files.')
    parser.add_argument('src_root', help='Source root directory to search')
    parser.add_argument('--dst_root', help='Target root directory to move files to (required if --move-files is specified)')
    parser.add_argument('--remove-rows', action='store_true', help='Remove warmup rows from CSV files')
    parser.add_argument('--move-files', action='store_true', help='Move warmup files to dst_root')
    parser.add_argument('--audit-log', default=None, help='Path to write the audit log JSON (timestamp will be added)')
    
    args = parser.parse_args()
    
    manage_warmup_data(
        args.src_root, 
        args.dst_root, 
        args.remove_rows, 
        args.move_files, 
        args.audit_log
    )
