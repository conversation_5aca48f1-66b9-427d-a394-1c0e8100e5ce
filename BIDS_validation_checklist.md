# BIDS Validation Checklist

This checklist helps ensure that your dataset adheres to the audio extension of the BIDS (Brain Imaging Data Structure) specification, focusing on structure, naming conventions, and required metadata files.

---

## 📁 Root Directory (`ds-<dataset_name>`)

- [ ] `dataset_description.json` is present and correctly formatted.
- [ ] `participants.tsv` contains required participant columns.
- [ ] `participants.json` provides metadata for `participants.tsv`.
- [ ] `README` is included to describe the dataset.
- [ ] `CHANGES` documents all dataset version updates.

---

## 📁 `phenotype/` Directory

- [ ] Each `.tsv` file has a corresponding `.json` file.
  - [ ] Check files like: `TPL.tsv`, `PCT.tsv`, `GFTA.tsv`, `PLSS.tsv`, `CELFS.tsv`, `CELFT.tsv`, `at_risk.tsv`.
  - [ ] Ensure matching JSON sidecars: `TPL.json`, `PCT.json`, etc.
- [ ] Each `.tsv` contains subject identifiers matching `participants.tsv`.

---

## 📁 `sub-*/beh/` Directory

### Raw Behavioral Data

- [ ] File naming follows `sub-*_task-*_acq-*_desc-rawdata.(wav|json)` convention.
- [ ] Uses consistent labels for:
  - `task-<task_label>`
  - `acq-<acquisition_label>` (e.g., MicX, MicY)
  - `desc-rawdata`
  - Optional `run-<index>` if multiple runs exist.
- [ ] Each `.wav` file has a corresponding `.json` metadata file.

---

## 📁 `derivatives/` Directory

### General

- [ ] Each subfolder (`pre_qc/`, `post_qc/`, `denoised_normalized/`) contains:
  - `dataset_description.json`
  - `sub-*/beh/` folders with derivative files.

### Pre-QC Derivatives

- [ ] File naming follows `sub-*_task-*_acq-*_desc-*.json`.
- [ ] Labels `task`, `acq`, `desc` must be consistent with raw data.

### Post-QC Derivatives

- [ ] File naming follows `sub-*_task-*_acq-*_desc-*.json`.

### Denoised & Normalized Data

- [ ] Files include `.wav` and `.json` for:
  - `sub-*_task-*_acq-*_desc-denoisednorm.(wav|json)`

---

## ✅ Label and Schema Validation

- [ ] All filenames follow BIDS label order: `sub-`, `task-`, `acq-`, `run-`, `desc-`, `suffix`.
- [ ] All metadata `.json` files are valid JSON and match the data files.
- [ ] No missing sidecars for `.tsv` or `.wav` files.
- [ ] Subject IDs and labels are consistent across all levels.

---

## 🔁 Consistency Checks

- [ ] Subjects listed in `participants.tsv` match `sub-*` folders.
- [ ] All tasks and acquisitions use standardized naming.
- [ ] Derivatives reflect valid transformations of raw data.
- [ ] JSON sidecars for derivatives include processing description.

---