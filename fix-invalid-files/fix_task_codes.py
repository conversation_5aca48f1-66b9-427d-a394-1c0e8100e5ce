#!/usr/bin/env python3
"""
Batch Rename Script for Task Code Fixes

Usage:
    python3 fix_task_codes.py [file_list.txt | --root ROOT_DIR] [--dry-run] [--log-dir LOG_DIR]

- file_list.txt: A text file with one file path per line.
- --root ROOT_DIR: Root directory to recursively search for files to rename.
- --dry-run: Show what would be done without making changes.
- --log-dir: Directory to write audit log (default: same as first file's directory or root directory).

Note: Provide either a file list OR a root directory, not both.
"""

import os
import re
import datetime
import json
import argparse

# Mapping from old code to new code
CODE_MAP = {
    # Standard mappings
    'warmup': 'WRM',
    'toyplay': 'TPL',
    'picturetask': 'PCT',
    'gfta.test': 'GFTA',
    'pls5.screen': 'PLSS',
    'celfp3.screen': 'CELFS',
    'celfp3.test': 'CELFT',
    'celfp3_test': 'CELFT',
    'ceflp3-screen': 'CELFS',
    'pls5_screen': 'PLSS',
}

def fix_task_code(filename):
    # Apply renaming rules using the CODE_MAP
    new_filename = filename
    for old, new in CODE_MAP.items():
        # Only replace if _task-<old> is present
        pattern = f'_task-{re.escape(old)}'
        if re.search(pattern, new_filename):
            new_filename = re.sub(pattern, f'_task-{new}', new_filename)
    return new_filename

def main():
    parser = argparse.ArgumentParser(description="Batch rename files based on task code rules.")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('file_list', nargs='?', help='Text file with one file path per line')
    group.add_argument('--root', help='Root directory to recursively search for files to rename')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    parser.add_argument('--log-dir', help='Directory to write audit log (default: same as first file\'s directory or root directory)')
    args = parser.parse_args()

    # Get file paths either from file list or by walking the root directory
    if args.file_list:
        with open(args.file_list, 'r') as f:
            file_paths = [line.strip() for line in f if line.strip()]
        default_log_dir = os.path.dirname(file_paths[0]) if file_paths else '.'
    else:  # args.root
        file_paths = []
        for dirpath, dirnames, filenames in os.walk(args.root):
            for filename in filenames:
                file_paths.append(os.path.join(dirpath, filename))
        default_log_dir = args.root

    log_entries = []

    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            continue

        filename = os.path.basename(file_path)
        dirpath = os.path.dirname(file_path)

        new_filename = fix_task_code(filename)
        if new_filename != filename:
            new_path = os.path.join(dirpath, new_filename)
            print(f"RENAME: {file_path} -> {new_path}")
            
            if not args.dry_run:
                try:
                    os.rename(file_path, new_path)
                    log_entries.append({
                        'old_path': file_path,
                        'new_path': new_path,
                        'timestamp': datetime.datetime.now().isoformat()
                    })
                except Exception as e:
                    print(f"  Error renaming {file_path}: {e}")
            else:
                # For dry run, still log what would be renamed
                log_entries.append({
                    'old_path': file_path,
                    'new_path': new_path,
                    'timestamp': datetime.datetime.now().isoformat(),
                    'dry_run': True
                })

    # Write audit log
    if log_entries:
        log_dir = args.log_dir or default_log_dir
        dry_run_suffix = "_dry_run" if args.dry_run else ""
        log_name = f"rename_audit{dry_run_suffix}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        log_path = os.path.join(log_dir, log_name)
        
        with open(log_path, 'w') as f:
            json.dump(log_entries, f, indent=2)
        print(f"Audit log written to {log_path}")
    else:
        print("No files renamed.")

if __name__ == "__main__":
    main()
