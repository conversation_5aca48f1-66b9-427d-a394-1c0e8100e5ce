#!/usr/bin/env python3
"""
Fix Task Run Numbering Script (BIDS-compliant entity order, robust)

This script finds files with:
- '-N' (e.g., -2) after the task code and renames them to use BIDS run numbering (run-0N), enforcing the correct BIDS entity order: task → acq → run → desc.
- 'run-<NN>' present but not in the correct BIDS order, and renames them to the correct order.
It also finds the corresponding original file (same name except without -N) and renames it to run-01. This works for all task types.

Example:
  sub-LAX007/beh/sub-LAX007_task-PLSS-2_acq-MicY_desc-rawdata.wav
    → sub-LAX007/beh/sub-LAX007_task-PLSS_acq-MicY_run-02_desc-rawdata.wav
  sub-LAX007/beh/sub-LAX007_task-PLSS_run-02_acq-MicY_desc-rawdata.wav
    → sub-LAX007/beh/sub-LAX007_task-PLSS_acq-MicY_run-02_desc-rawdata.wav
  sub-LAX007/beh/sub-LAX007_task-PLSS_acq-MicY_desc-rawdata.wav
    → sub-LAX007/beh/sub-LAX007_task-PLSS_acq-MicY_run-01_desc-rawdata.wav

Usage:
  python3 fix_task_run_numbering.py <root_directory> [--dry-run]
"""

import os
import re
import argparse
from pathlib import Path

def parse_bids_entities(filename):
    """Parse BIDS entities from a filename and return a dict."""
    # Standard BIDS parsing first
    pattern = (
        r'(?P<sub>sub-[^_]+)'
        r'(?:_ses-[^_]+)?'
        r'_task-(?P<task>[^_]+)'
        r'(?:_acq-(?P<acq>[^_]+))?'
        r'(?:_run-(?P<run>\d+))?'
        r'(?:_recording-[^_]+)?'
        r'(?:_desc-(?P<desc>[^.]+))?'
        r'(?P<suffix>\.[^.]+)$'
    )
    m = re.search(pattern, filename)
    if m:
        return m.groupdict()
    
    # Try to find run in the wrong place (e.g., after task or before acq)
    # Accept any order of _acq, _run, _desc
    entity_pat = r'(?P<sub>sub-[^_]+)_task-(?P<task>[^_]+)(?P<rest>.*?)(?P<suffix>\.[^.]+)$'
    m2 = re.match(entity_pat, filename)
    if m2:
        rest = m2.group('rest')
        suffix = m2.group('suffix')
        # Find all entities in rest
        acq = re.search(r'_acq-([^_]+)', rest)
        run = re.search(r'_run-(\d+)', rest)
        desc = re.search(r'_desc-([^.]+)', rest)
        return {
            'sub': m2.group('sub'),
            'task': m2.group('task'),
            'acq': acq.group(1) if acq else None,
            'run': run.group(1) if run else None,
            'desc': desc.group(1) if desc else None,
            'suffix': suffix
        }
    
    return None

def build_bids_filename(entities):
    """Build a BIDS filename from entity dict, enforcing correct order."""
    parts = [entities['sub']]
    parts.append(f"task-{entities['task']}")
    if entities.get('acq'):
        parts.append(f"acq-{entities['acq']}")
    if entities.get('run'):
        parts.append(f"run-{int(entities['run']):02d}")
    if entities.get('desc'):
        parts.append(f"desc-{entities['desc']}")
    filename = '_'.join(parts) + entities['suffix']
    return filename

def find_files_to_fix(root_dir):
    """Find files with -N after task, or run-<NN> in the wrong place, or missing run-01."""
    files_to_fix = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            full_path = os.path.join(root, file)
            print(f"CHECKING: {full_path}")
            # 1. Old style: _task-<TASK>-N_ or _task-<TASK>.N_
            m_old = re.search(r'(_task-[A-Za-z0-9]+)[.-](\d+)(_acq-[^_]+)?(_desc-[^.]+)?(\.[^.]+)$', file)
            if m_old:
                print(f"MATCH (old style): {full_path}")
                # Remove the dot/dash and number from the original filename
                orig_file = re.sub(r'(_task-[A-Za-z0-9]+)[.-](\d+)', r'\1', file)
                files_to_fix.append({'root': root, 'file': file, 'run_num': m_old.group(2), 'orig_file': orig_file})
                continue
            # 2. Out-of-order run: run-<NN> before acq or desc
            entities = parse_bids_entities(file)
            if entities and entities.get('run'):
                correct_name = build_bids_filename(entities)
                if file != correct_name:
                    print(f"MATCH (out-of-order run): {full_path}")
                    files_to_fix.append({'root': root, 'file': file, 'run_num': entities['run'], 'orig_file': None})
                    continue
            # 3. Original file (no run) that should be run-01 if a run-02 exists
            # (Handled in main loop)
            print(f"SKIP (no fix needed): {full_path}")
    return files_to_fix

def main():
    parser = argparse.ArgumentParser(description="Fix task run numbering in filenames (BIDS order, robust).")
    parser.add_argument('root_dir', help='Root directory to scan for files')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be renamed without making changes')
    args = parser.parse_args()

    files_to_fix = find_files_to_fix(args.root_dir)
    processed = set()

    for entry in files_to_fix:
        root = entry['root']
        file = entry['file']
        run_num = entry['run_num']
        old_path = Path(root) / file
        entities = parse_bids_entities(file)
        if not entities:
            print(f"SKIP (unparsable): {old_path}")
            continue
        entities['run'] = run_num
        new_file = build_bids_filename(entities)
        new_path = Path(root) / new_file
        if old_path != new_path:
            print(f"RENAME: {old_path} -> {new_path}")
            if not args.dry_run:
                os.rename(old_path, new_path)
            processed.add((root, new_file))
        else:
            print(f"SKIP (already correct): {old_path}")
        # Now handle the original file (without -N) if present
        orig_file = entry.get('orig_file')
        if orig_file:
            orig_path = Path(root) / orig_file
            if orig_path.exists():
                orig_entities = parse_bids_entities(orig_file)
                if orig_entities:
                    orig_entities['run'] = '01'
                    orig_new_file = build_bids_filename(orig_entities)
                    orig_new_path = Path(root) / orig_new_file
                    if (root, orig_new_file) not in processed and orig_path != orig_new_path:
                        print(f"RENAME: {orig_path} -> {orig_new_path}")
                        if not args.dry_run:
                            os.rename(orig_path, orig_new_path)
                        processed.add((root, orig_new_file))
                    else:
                        print(f"SKIP (already correct or processed): {orig_path}")

if __name__ == "__main__":
    main() 