#!/usr/bin/env python3
"""
Fix BIDS Naming Conventions Script

This script fixes various case and formatting issues in BIDS-compliant file naming:
1. Microphone acquisition code case (micX → MicX, micY → MicY, micXY → MicXY)
2. QC labels (postqc → PostQC, preqc → PreQC)
3. General BIDS naming convention fixes (e.g., desc formatting, acquisition label case)
4. Various file name patterns from legacy naming schemes

Scans recursively through a directory, creates detailed audit logs, and supports dry-run mode.

Usage:
    python3 fix_naming_conventions.py <root_directory> [--dry-run] [--fix-mic] [--fix-qc] [--fix-general] [--all]

Example:
    python3 fix_naming_conventions.py /path/to/dataset --all
    python3 fix_naming_conventions.py /path/to/dataset --fix-mic --dry-run
"""

import os
import re
import argparse
import datetime
import json
from pathlib import Path

# ============================================================================
# Filename Fixers
# ============================================================================

def fix_microphone_case(filename):
    """
    Fix microphone acquisition codes in filename.
    Changes: micX → MicX, micY → MicY, micXY → MicXY
    """
    # Define the replacements
    replacements = {
        'micX': 'MicX',
        'micY': 'MicY', 
        'micXY': 'MicXY'
    }
    
    new_filename = filename
    changes_made = []
    
    # Apply each replacement
    for old, new in replacements.items():
        if old in new_filename:
            new_filename = new_filename.replace(old, new)
            changes_made.append(f"{old} → {new}")
    
    return new_filename, changes_made

def fix_qc_case(filename):
    """
    Fix QC labels in filename.
    Changes: postqc → PostQC, preqc → PreQC
    """
    new_filename = filename
    changes_made = []
    
    if 'postqc' in new_filename:
        new_filename = new_filename.replace('postqc', 'PostQC')
        changes_made.append('postqc → PostQC')
    if 'preqc' in new_filename:
        new_filename = new_filename.replace('preqc', 'PreQC')
        changes_made.append('preqc → PreQC')
    
    return new_filename, changes_made

def fix_general_conventions(filename):
    """
    Fix general BIDS naming conventions in filename, including:
    - Proper desc tags (_desc-rawdata, _desc-denoisednorm)
    - Capitalization of acquisition labels
    - Multiple underscores
    - And more
    """
    new_filename = filename
    changes_made = []
    
    # Define the replacements
    replacements = [
        (r'micxy', 'MicXY'),
        (r'micx', 'MicX'),
        (r'micy', 'MicY'),
        (r'acq-za', 'acq-ZA'),
        # General fallback (should not trigger if below are correct)
        (r'RawData', 'rawdata'),
        (r'DenoisedNormalized', 'denoisednorm'),
        (r'DenoisedNormalised', 'denoisednorm'),
        # denoisednorm_preqc -> denoisednormPreQC
        (r'denoisednorm_preqc', 'denoisednormPreQC'),
        # denoisednorm_postqc -> denoisednormPostQC
        (r'denoisednorm_postqc', 'denoisednormPostQC'),
    ]
    
    # Track original filename to detect changes
    original_filename = new_filename
    
    # Only apply _desc-rawdata if not already present
    if '_desc-rawdata' not in new_filename:
        temp = re.sub(r'_(RawData|rawdata)', r'_desc-rawdata', new_filename)
        if temp != new_filename:
            changes_made.append(f"Added _desc-rawdata tag")
            new_filename = temp
    
    # Only apply _desc-denoisednorm if not already present
    if '_desc-denoisednorm' not in new_filename:
        temp = re.sub(r'_(DenoisedNormalized|DenoisedNormalised)', r'_desc-denoisednorm', new_filename)
        if temp != new_filename:
            changes_made.append(f"Added _desc-denoisednorm tag")
            new_filename = temp
    
    # Now apply the rest of the replacements
    for pattern, repl in replacements:
        temp = re.sub(pattern, repl, new_filename)
        if temp != new_filename:
            changes_made.append(f"{pattern} → {repl}")
            new_filename = temp
    
    # Collapse any sequence of _desc- (e.g., _desc-_desc-) into a single _desc-
    temp = re.sub(r'(_desc-)+', r'_desc-', new_filename)
    if temp != new_filename:
        changes_made.append("Collapsed multiple _desc- tags")
        new_filename = temp
    
    # Ensure _desc- precedes denoisednormPreQC and denoisednormPostQC if not already present
    temp = re.sub(r'(?<!_desc-)denoisednormPreQC', r'_desc-denoisednormPreQC', new_filename)
    if temp != new_filename:
        changes_made.append("Added missing _desc- before denoisednormPreQC")
        new_filename = temp
    
    temp = re.sub(r'(?<!_desc-)denoisednormPostQC', r'_desc-denoisednormPostQC', new_filename)
    if temp != new_filename:
        changes_made.append("Added missing _desc- before denoisednormPostQC")
        new_filename = temp
    
    # Collapse any sequence of multiple underscores to a single underscore
    temp = re.sub(r'_+', r'_', new_filename)
    if temp != new_filename:
        changes_made.append("Collapsed multiple underscores")
        new_filename = temp
    
    # Replace double underscores in __desc with a single underscore
    if '__desc' in new_filename:
        temp = new_filename.replace('__desc', '_desc')
        if temp != new_filename:
            changes_made.append("Fixed __desc → _desc")
            new_filename = temp
    
    return new_filename, changes_made

def apply_all_fixes(filename):
    """Apply all available filename fixes and combine their changes."""
    # Start with the original filename
    new_filename = filename
    all_changes = []
    
    # Apply each fix sequentially
    fixed_filename, changes = fix_microphone_case(new_filename)
    new_filename = fixed_filename
    all_changes.extend(changes)
    
    fixed_filename, changes = fix_qc_case(new_filename)
    new_filename = fixed_filename
    all_changes.extend(changes)
    
    fixed_filename, changes = fix_general_conventions(new_filename)
    new_filename = fixed_filename
    all_changes.extend(changes)
    
    return new_filename, all_changes

# ============================================================================
# File Processing Logic
# ============================================================================

def process_files(root_dir, fix_funcs, dry_run=False):
    """
    Process files recursively using the specified fix functions.
    
    Args:
        root_dir: Root directory to scan
        fix_funcs: Dictionary mapping fix type to fix function
        dry_run: Whether to perform a dry run (no actual changes)
    
    Returns:
        Tuple of (audit_log, total_files_checked, total_files_renamed)
    """
    root_path = Path(root_dir)
    audit_log = []
    total_files_checked = 0
    total_files_renamed = 0
    still_lowercase_files = []
    
    print(f"Scanning directory: {root_path}")
    print(f"Dry run mode: {'ON' if dry_run else 'OFF'}")
    print(f"Fixes enabled: {', '.join(fix_funcs.keys())}")
    print("-" * 60)
    
    for root, dirs, files in os.walk(root_path):
        for filename in files:
            total_files_checked += 1
            
            # Apply all enabled fixes
            new_filename = filename
            all_changes = []
            
            for fix_type, fix_func in fix_funcs.items():
                fixed_filename, changes = fix_func(new_filename)
                new_filename = fixed_filename
                all_changes.extend(changes)
            
            # If changes were made, process the rename
            if new_filename != filename:
                old_path = Path(root) / filename
                new_path = Path(root) / new_filename
                
                # Check if target file already exists (other than case-only difference)
                if new_path.exists() and str(old_path).lower() != str(new_path).lower():
                    audit_log.append({
                        'old_path': str(old_path),
                        'new_path': str(new_path),
                        'changes': all_changes,
                        'status': 'skipped_target_exists',
                        'timestamp': datetime.datetime.now().isoformat()
                    })
                    print(f"⚠️  SKIP: Target exists - {filename} → {new_filename}")
                else:
                    if not dry_run:
                        try:
                            # If only the case is changing, do a two-step rename
                            if str(old_path).lower() == str(new_path).lower():
                                tmp_path = Path(root) / (new_filename + ".tmpcasefix")
                                old_path.rename(tmp_path)
                                tmp_path.rename(new_path)
                            else:
                                old_path.rename(new_path)
                            total_files_renamed += 1
                            audit_log.append({
                                'old_path': str(old_path),
                                'new_path': str(new_path),
                                'changes': all_changes,
                                'status': 'renamed',
                                'timestamp': datetime.datetime.now().isoformat()
                            })
                            print(f"✅ RENAMED: {filename} → {new_filename}")
                        except Exception as e:
                            audit_log.append({
                                'old_path': str(old_path),
                                'new_path': str(new_path),
                                'changes': all_changes,
                                'status': 'error',
                                'error': str(e),
                                'timestamp': datetime.datetime.now().isoformat()
                            })
                            print(f"❌ ERROR: {filename} - {str(e)}")
                    else:
                        total_files_renamed += 1
                        audit_log.append({
                            'old_path': str(old_path),
                            'new_path': str(new_path),
                            'changes': all_changes,
                            'status': 'would_rename',
                            'timestamp': datetime.datetime.now().isoformat()
                        })
                        print(f"🔍 WOULD RENAME: {filename} → {new_filename}")
            
            # Check for remaining lowercase issues if microphone fixes were enabled
            if 'microphone' in fix_funcs and any(pattern in new_filename for pattern in ['micX', 'micY', 'micXY']):
                still_lowercase_files.append(str(Path(root) / new_filename))
    
    # After processing, log any files that still contain micX, micY, or micXY
    if 'microphone' in fix_funcs and still_lowercase_files:
        print("\n⚠️  Files still containing lowercase micX, micY, or micXY:")
        for f in still_lowercase_files:
            print(f"  {f}")
        # Save to a log file
        lowercase_log_path = root_path / "mic_lowercase_remaining_files.txt"
        with open(lowercase_log_path, 'w') as f:
            for line in still_lowercase_files:
                f.write(line + '\n')
        print(f"\nList saved to: {lowercase_log_path}")
    
    return audit_log, total_files_checked, total_files_renamed

# ============================================================================
# Reporting Functions
# ============================================================================

def generate_audit_report(audit_log, total_checked, total_renamed, root_dir, enabled_fixes, dry_run=False):
    """
    Generate a comprehensive audit report.
    """
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    mode = "dry_run" if dry_run else "execution"
    
    # Count different statuses
    renamed_count = len([entry for entry in audit_log if entry['status'] == 'renamed'])
    would_rename_count = len([entry for entry in audit_log if entry['status'] == 'would_rename'])
    skipped_count = len([entry for entry in audit_log if entry['status'] == 'skipped_target_exists'])
    error_count = len([entry for entry in audit_log if entry['status'] == 'error'])
    
    # Summary statistics
    summary = {
        'timestamp': datetime.datetime.now().isoformat(),
        'root_directory': str(root_dir),
        'enabled_fixes': list(enabled_fixes),
        'dry_run': dry_run,
        'total_files_checked': total_checked,
        'total_files_renamed': total_renamed,
        'renamed_files': renamed_count,
        'would_rename_files': would_rename_count,
        'skipped_files': skipped_count,
        'error_files': error_count,
        'changes_made': []
    }
    
    # Collect all unique changes made
    all_changes = set()
    for entry in audit_log:
        if 'changes' in entry:
            all_changes.update(entry['changes'])
    summary['changes_made'] = list(all_changes)
    
    return summary, audit_log

def save_audit_files(summary, audit_log, root_dir, dry_run=False):
    """
    Save audit report and detailed log to files.
    """
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    mode = "dry_run" if dry_run else "execution"
    
    # Create audit directory if it doesn't exist
    audit_dir = Path(root_dir) / "audit_logs"
    audit_dir.mkdir(exist_ok=True)
    
    # Save summary report
    summary_filename = f"naming_convention_fix_summary_{mode}_{timestamp}.json"
    summary_path = audit_dir / summary_filename
    
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Save detailed audit log
    log_filename = f"naming_convention_fix_audit_{mode}_{timestamp}.json"
    log_path = audit_dir / log_filename
    
    with open(log_path, 'w') as f:
        json.dump(audit_log, f, indent=2)
    
    return summary_path, log_path

def print_summary(summary, audit_log):
    """
    Print a human-readable summary of the operation.
    """
    print("\n" + "=" * 60)
    print("NAMING CONVENTION FIX SUMMARY")
    print("=" * 60)
    print(f"Root Directory: {summary['root_directory']}")
    print(f"Timestamp: {summary['timestamp']}")
    print(f"Enabled Fixes: {', '.join(summary['enabled_fixes'])}")
    print(f"Dry Run: {'Yes' if summary['dry_run'] else 'No'}")
    print(f"Total Files Checked: {summary['total_files_checked']:,}")
    print(f"Total Files Renamed: {summary['total_files_renamed']:,}")
    print(f"Renamed Files: {summary['renamed_files']:,}")
    print(f"Would Rename Files: {summary['would_rename_files']:,}")
    print(f"Skipped Files: {summary['skipped_files']:,}")
    print(f"Error Files: {summary['error_files']:,}")
    
    if summary['changes_made']:
        print(f"\nChanges Made:")
        for change in summary['changes_made']:
            print(f"  - {change}")
    
    # Show some examples
    renamed_files = [entry for entry in audit_log if entry['status'] in ['renamed', 'would_rename']]
    if renamed_files:
        print(f"\nSample Renamed Files:")
        for entry in renamed_files[:5]:  # Show first 5
            old_name = Path(entry['old_path']).name
            new_name = Path(entry['new_path']).name
            print(f"  - {old_name} → {new_name}")
        
        if len(renamed_files) > 5:
            print(f"  ... and {len(renamed_files) - 5} more")
    
    if summary['skipped_files'] > 0:
        print(f"\n⚠️  {summary['skipped_files']} files were skipped because target files already exist")
    
    if summary['error_files'] > 0:
        print(f"\n❌ {summary['error_files']} files had errors during renaming")

# ============================================================================
# Main Function
# ============================================================================

def main():
    parser = argparse.ArgumentParser(
        description="Fix BIDS naming conventions in filenames",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 fix_naming_conventions.py /path/to/dataset --all
  python3 fix_naming_conventions.py /path/to/dataset --fix-mic --dry-run
  python3 fix_naming_conventions.py /path/to/dataset --fix-qc
  python3 fix_naming_conventions.py /path/to/dataset --fix-general
        """
    )
    
    parser.add_argument('root_dir', help='Root directory to scan for files')
    parser.add_argument('--dry-run', action='store_true', 
                        help='Show what would be renamed without actually renaming files')
    
    # Fix type options
    fix_group = parser.add_argument_group('Fix Options')
    fix_group.add_argument('--fix-mic', action='store_true',
                          help='Fix microphone case issues (micX → MicX)')
    fix_group.add_argument('--fix-qc', action='store_true',
                          help='Fix QC case issues (postqc → PostQC, preqc → PreQC)')
    fix_group.add_argument('--fix-general', action='store_true',
                          help='Fix general BIDS naming conventions (desc tags, multiple underscores, etc.)')
    fix_group.add_argument('--all', action='store_true',
                          help='Apply all available fixes')
    
    args = parser.parse_args()
    
    # Validate root directory
    if not os.path.exists(args.root_dir):
        print(f"Error: Directory does not exist: {args.root_dir}")
        return 1
    
    if not os.path.isdir(args.root_dir):
        print(f"Error: Path is not a directory: {args.root_dir}")
        return 1
    
    # Determine which fixes to apply
    fix_funcs = {}
    if args.all or args.fix_mic:
        fix_funcs['microphone'] = fix_microphone_case
    if args.all or args.fix_qc:
        fix_funcs['qc'] = fix_qc_case
    if args.all or args.fix_general:
        fix_funcs['general'] = fix_general_conventions
    
    # If no fixes specified, show error and exit
    if not fix_funcs:
        print("Error: No fixes specified. Use --fix-mic, --fix-qc, or --all")
        return 1
    
    try:
        # Process files
        audit_log, total_checked, total_renamed = process_files(
            args.root_dir, fix_funcs, args.dry_run
        )
        
        # Generate audit report
        summary, audit_log = generate_audit_report(
            audit_log, total_checked, total_renamed, args.root_dir, fix_funcs.keys(), args.dry_run
        )
        
        # Save audit files
        summary_path, log_path = save_audit_files(
            summary, audit_log, args.root_dir, args.dry_run
        )
        
        # Print summary
        print_summary(summary, audit_log)
        
        print(f"\n📄 Audit files saved:")
        print(f"  Summary: {summary_path}")
        print(f"  Detailed log: {log_path}")
        
        if args.dry_run:
            print(f"\n💡 To actually rename files, run without --dry-run flag")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
