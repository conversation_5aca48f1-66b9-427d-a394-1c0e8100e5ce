#!/usr/bin/env python3
"""
Find Invalid Task Codes Script

This script recursively searches for files with '_task-' in their name, but where the following substring is not one of the allowed task codes:
    CELFS, CELFT, GFTA, PCT, PLSS, TPL
It ignores any files in a 'large_files' folder.

Usage:
    python3 find_invalid_task_codes.py <root_directory>

Output:
    - Prints invalid files to the console
    - Saves a JSON log of invalid files found
"""

import os
import re
import argparse
import json
from pathlib import Path

def find_invalid_task_files(root_dir, allowed_tasks):
    invalid_files = []
    task_pattern = re.compile(r'_task-([A-Za-z0-9]+)')
    
    for root, dirs, files in os.walk(root_dir):
        # Skip any 'large_files' folder
        if 'large_files' in root.split(os.sep):
            continue
        for file in files:
            if '_task-' in file:
                match = task_pattern.search(file)
                if match:
                    task_code = match.group(1).upper()
                    if task_code not in allowed_tasks:
                        invalid_files.append({
                            'file_path': str(Path(root) / file),
                            'task_code': task_code
                        })
    return invalid_files

def main():
    parser = argparse.ArgumentParser(description="Find files with invalid task codes in filenames.")
    parser.add_argument('root_dir', help='Root directory to scan for files')
    args = parser.parse_args()
    
    allowed_tasks = {'CELFS', 'CELFT', 'GFTA', 'PCT', 'PLSS', 'TPL'}
    
    invalid_files = find_invalid_task_files(args.root_dir, allowed_tasks)
    
    print(f"Found {len(invalid_files)} files with invalid task codes:")
    for entry in invalid_files:
        print(f"  {entry['file_path']} (task: {entry['task_code']})")
    
    # Save log
    log_path = Path(args.root_dir) / 'invalid_task_code_files.json'
    with open(log_path, 'w') as f:
        json.dump(invalid_files, f, indent=2)
    print(f"\nLog saved to: {log_path}")

if __name__ == "__main__":
    main() 