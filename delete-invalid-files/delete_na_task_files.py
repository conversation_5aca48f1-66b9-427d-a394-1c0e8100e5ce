#!/usr/bin/env python3
"""
Batch Delete Script for Specific Task Codes

Usage:
    python3 delete_na_task_files.py file_list.txt [--dry-run]

- file_list.txt: A text file with one file path per line.
- --dry-run: Show what would be done without making changes.
"""

import os
import re
import argparse

def should_delete(filename):
    # Delete if _task-NA
    if '_task-NA' in filename:
        return True
    # Delete if _task- followed by any permutation of ABCD (case-insensitive)
    match = re.search(r'_task-([A-Za-z]{4})', filename)
    if match:
        code = match.group(1).upper()
        if sorted(code) == ['A', 'B', 'C', 'D']:
            return True
    return False

def main():
    parser = argparse.ArgumentParser(description="Batch delete files based on task code rules.")
    parser.add_argument('file_list', help='Text file with one file path per line')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    args = parser.parse_args()

    with open(args.file_list, 'r') as f:
        file_paths = [line.strip() for line in f if line.strip()]

    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            continue

        filename = os.path.basename(file_path)

        if should_delete(filename):
            print(f"DELETE: {file_path}")
            if not args.dry_run:
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"  Error deleting {file_path}: {e}")

if __name__ == "__main__":
    main()
